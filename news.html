<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{companyInfo.name}}</title>
    <!-- favicons Icons -->




    <meta name="description" content="Insur HTML 5 Template ">

    <!-- fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com/">

    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">

    <link href="static/css/css2.css" rel="stylesheet">


    <link rel="stylesheet" href="static/css/bootstrap.min.css">
    <link rel="stylesheet" href="static/css/animate.min.css">
    <link rel="stylesheet" href="static/css/custom-animate.css">
    <link rel="stylesheet" href="static/css/all.min.css">
    <link rel="stylesheet" href="static/css/jarallax.css">
    <link rel="stylesheet" href="static/css/jquery.magnific-popup.css">
    <link rel="stylesheet" href="static/css/nouislider.min.css">
    <link rel="stylesheet" href="static/css/nouislider.pips.css">
    <link rel="stylesheet" href="static/css/odometer.min.css">
    <link rel="stylesheet" href="static/css/swiper.min.css">
    <link rel="stylesheet" href="static/css/style.css">
    <link rel="stylesheet" href="static/css/style1.css">
    <link rel="stylesheet" href="static/css/tiny-slider.min.css">
    <link rel="stylesheet" href="static/css/stylesheet.css">
    <link rel="stylesheet" href="static/css/owl.carousel.min.css">
    <link rel="stylesheet" href="static/css/owl.theme.default.min.css">
    <link rel="stylesheet" href="static/css/jquery.bxslider.css">
    <link rel="stylesheet" href="static/css/bootstrap-select.min.css">
    <link rel="stylesheet" href="static/css/vegas.min.css">
    <link rel="stylesheet" href="static/css/jquery-ui.css">
    <link rel="stylesheet" href="static/css/timePicker.css">

    <!-- template styles -->
    <link rel="stylesheet" href="static/css/insur.css">
    <link rel="stylesheet" href="static/css/insur-responsive.css">

    <!-- 认证组件样式 -->
    <link rel="stylesheet" href="static/css/auth-components.css">

    <!-- 添加新闻列表样式 -->
    <style>
        /* News Tabs - Design Master Polish */
        .news-list-tabs {
            margin-bottom: 25px;
        }

        .news-list-tabs__nav {
            display: flex;
            padding-bottom: 5px;
            margin-bottom: 35px; /* Slightly more space below tabs */
            overflow-x: auto;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .news-list-tabs__nav::-webkit-scrollbar {
            display: none;
        }

        .news-list-tabs__item {
            padding: 8px 20px; /* Refined padding */
            cursor: pointer;
            border-radius: 20px;
            font-size: 16px;
            font-weight: 500;
            color: #666; /* Slightly darker grey for inactive text */
            background-color: transparent;
            transition: all 0.2s ease-in-out; /* Faster transition */
            white-space: nowrap;
            margin-right: 8px; /* Slightly reduce space between tabs */
            border: 1px solid transparent;
        }

        .news-list-tabs__item:hover {
           color: #015fc9;
        }

        .news-list-tabs__item.active {
            background-color: #015fc9;
            color: #ffffff;
            border-color: #015fc9;
        }
        .news-list-tabs__item.active:hover {
           color: #ffffff;
        }

        /* News List Content - Design Master Polish */
        .news-list-content {
            display: none;
        }

        .news-list-content.active {
            display: block;
        }

        .news-list {
            /* No styles */
        }

        .news-list__item {
            padding: 20px 0; /* Increased padding */
            border-bottom: 1px solid #f0f0f0; /* Very light border */
            cursor: pointer;
            transition: background-color 0.2s ease-in-out;
        }

        .news-list__item:last-child {
            border-bottom: none;
        }

        .news-list__item:hover {
            background-color: #f5f5f5; /* Slightly darker background */
            transform: scale(0.99); /* Slightly scale down for sinking effect */
            box-shadow: inset 0 1px 4px rgba(0, 0, 0, 0.1); /* Inset shadow for pressed look */
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out, background-color 0.2s ease-in-out; /* Include transform in transition */
        }

        .news-list__item > div:first-of-type {
            margin-bottom: 10px; /* Increased space */
        }

        .news-list__category {
            display: inline-block;
            background-color: #eaf2fc; /* Light blue background like image */
            color: #3c77c9; /* Darker blue text like image */
            padding: 4px 10px; /* Match image padding */
            border-radius: 4px;
            font-size: 13px; /* Slightly larger tag text */
            font-weight: 500;
            margin-right: 12px; /* More space after tag */
            white-space: nowrap;
            vertical-align: middle;
        }

        .news-list__title {
            display: inline;
            font-size: 18px;
            font-weight: 600; /* Bolder title */
            color: #333;
            overflow: hidden;
            text-overflow: ellipsis;
            vertical-align: middle;
            line-height: 1.5; /* Adjust line height */
        }
        .news-list__item:hover .news-list__title {
            color: #015fc9;
        }

        .news-list__meta {
            align-items: center;
            color: #999; /* Lighter grey for meta */
            font-size: 13px; /* Smaller meta font */
        }

        .news-list__meta i {
            margin-right: 6px; /* Adjust icon spacing */
            vertical-align: middle;
            font-size: 14px; /* Ensure icon size is consistent */
        }

        .news-list__date {
            margin-right: 25px; /* More space between date and views */
        }

        /* Responsive Adjustments - Design Master Polish */
        @media (max-width: 767px) {
            .news-list-tabs__nav {
                justify-content: flex-start;
                padding-bottom: 5px;
                margin-bottom: 25px; /* Adjust spacing */
            }

            .news-list-tabs__item {
                padding: 6px 14px; /* Adjust padding */
                font-size: 15px;
                margin-right: 5px;
            }

            .news-list__item {
                padding: 15px 0;
                flex-wrap: wrap;
            }

            .news-list__item > div:first-of-type {
                margin-bottom: 8px; /* Adjust space */
            }

            .news-list__category {
                font-size: 12px;
                padding: 3px 8px;
                margin-right: 8px;
            }

            .news-list__title {
                font-size: 16px;
                font-weight: 600;
                white-space: normal;
                display: inline;
                line-height: 1.45;
            }

            .news-list__meta {
                justify-content: flex-start;
                font-size: 12px; /* Smaller meta on mobile */
                color: #aaa; /* Even lighter meta on mobile */
            }
             .news-list__date {
                margin-right: 15px; /* Adjust spacing */
             }
             .news-list__meta i {
                 font-size: 13px; /* Adjust icon size */
             }
        }
        .pagination-container {
            text-align: center;
            margin-top: 40px;
        }

        .pagination-btn {
            display: inline-block;
            padding: 8px 16px;
            margin: 0 5px;
            border: 1px solid #015fc9;
            color: #015fc9;
            background-color: #fff;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s, color 0.2s;
        }

        .pagination-btn:hover,
        .pagination-btn.active {
            background-color: #015fc9;
            color: #fff;
        }

        .pagination-btn:disabled {
            border-color: #ccc;
            color: #ccc;
            background-color: #f9f9f9;
            cursor: not-allowed;
        }

        .pagination-info {
            display: inline-block;
            margin: 0 15px;
            color: #666;
            font-size: 14px;
            vertical-align: middle;
        }

        @media (max-width: 767px) {
            .pagination-btn {
                padding: 6px 12px;
                font-size: 14px;
                margin: 0 3px;
            }
            .pagination-info {
                font-size: 12px;
                margin: 0 8px;
            }
        }
    </style>
</head>

<body class="custom-cursor">

    <div class="custom-cursor__cursor"></div>
    <div class="custom-cursor__cursor-two"></div>





    <div class="preloader">
        <div class="preloader__image"></div>
    </div>
    <!-- /.preloader -->

    <div id="app">
    <div class="page-wrapper">
        <header class="main-header clearfix">
            <div class="main-header__top">
                <div class="container">
                    <div class="main-header__top-inner">
                        <div class="main-header__top-address">
                            <ul class="list-unstyled main-header__top-address-list">
                                <li>
                                    <i class="icon">
                                        <span class="icon-pin"></span>
                                    </i>
                                    <div class="text">
                                        <p>{{companyInfo.address}}</p>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon">
                                        <span class="icon-email"></span>
                                    </i>
                                    <div class="text">
                                        <p><a href="mailto:<EMAIL>">{{companyInfo.mailbox}}</a></p>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <nav class="main-menu clearfix">
                <div class="main-menu__wrapper clearfix">
                    <div class="container">
                        <div class="main-menu__wrapper-inner clearfix">
                            <div class="main-menu__left">
                                <div class="main-menu__logo">
                                    <a><img :src="companyInfo.logoImg" alt="" style="height: 27px"></a>
                                </div>
                                <div class="main-menu__main-menu-box">
                                    <div class="main-menu__main-menu-box-inner">
                                        <a href="#" class="mobile-nav__toggler" @click="expanded = true"><i class="fa fa-bars"></i></a>
                                        <ul class="main-menu__list one-page-scroll-menu">
                                            <li class=" megamenu scrollToLink">
                                                <a href="index-one-page.html">首页 </a>
                                            </li>

                                            <li class="scrollToLink">
                                                <a href="company.html">保险公司 </a>
                                            </li>
                                            <li class="scrollToLink">
                                                <a href="products.html">保险产品</a>
                                            </li>
                                            <li class="scrollToLink current">
                                                <a href="news.html">新闻资讯</a>
                                            </li>
                                            <li class="scrollToLink" style="margin-right: 37px;">
                                                <a href="about.html">关于我们</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <!-- 右侧用户头像区域 -->
                            <user-avatar
                                :is-logged-in="isLoggedIn"
                                :show-user-menu="showUserMenu"
                                @user-click="handleUserClick"
                                @logout-confirm="showLogoutConfirm">
                            </user-avatar>
                        </div>
                    </div>
                </div>
            </nav>
        </header>

        <div class="stricky-header stricked-menu main-menu">
            <div class="sticky-header__content"></div><!-- /.sticky-header__content -->
        </div><!-- /.stricky-header -->

        <!--Page Header Start-->
        <section class="page-header">
            <div class="page-header-bg" style="background-image: url(assets/images/backgrounds/page-header-bg.jpg)">
            </div>
            <div class="page-header-shape-1"><img src="static/picture/page-header-shape-1.png" alt=""></div>
            <div class="container">
                <div class="page-header__inner">
                    <!-- <ul class="thm-breadcrumb list-unstyled">
                        <li><a>Home</a></li>
                        <li><span>/</span></li>
                        <li>news</li>
                    </ul> -->
                    <h2>新闻资讯</h2>
                </div>
            </div>
        </section>
        <!--Page Header End-->

        <!--News One Start-->
        <section class="news-one" id="news">
            <div class="container">
                <div class="col-xl-4 col-lg-5" style="margin-bottom: 40px;">
                    <div class="sidebar__single sidebar__search">
                        <div class="sidebar__search-form">
                            <input v-model="keyword" type="search" placeholder="搜索新闻名称">
                            <button @click="getNews" type="submit"><i class="icon-magnifying-glass"></i></button>
                        </div>
                    </div>
                </div>
                <div class="section-title text-center">
                    <div class="section-sub-title-box">
                        <p class="section-sub-title">新闻资讯</p>
                        <div class="section-title-shape-1">
                            <img src="static/picture/section-title-shape-1.png" alt="">
                        </div>
                        <div class="section-title-shape-2">
                            <img src="static/picture/section-title-shape-2.png" alt="">
                        </div>
                    </div>
                    <h2 class="section-title__title">{{companyInfo.companyName}}动态</h2>
                </div>

                <!-- News List Area -->
                <div class="news-list-content active">
                    <div class="news-list">
                        <!-- Display message if no news -->
                        <div v-if="!newsList || newsList.length === 0" style="text-align: center; padding: 40px; color: #888;">
                            暂无相关资讯
                        </div>
                        <!-- Loop through newsList directly -->
                        <div class="news-list__item" v-for="(item,index) in newsList" @click="toDetail(item)" :key="item.id || index">
                            <div>
                                <!-- Removed category span -->
                                <span class="news-list__title">{{item.name}}</span>
                            </div>
                            <div class="news-list__meta">
                                <!-- 只有当 newsTime 不为 null 时才显示日期和日期图标 -->
                                <span class="news-list__date" v-if="item.newsTime"><i class="far fa-calendar"></i> {{item.newsTime}}</span>
                                <span class="news-list__views"><i class="far fa-eye"></i> {{item.clicksNum || 0}}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="pagination-container" v-if="totalPages > 1">
                    <button class="pagination-btn" @click="changePage(currentPage - 1)" :disabled="currentPage === 1">
                        上一页
                    </button>
                    <span class="pagination-info">第 {{ currentPage }} / {{ totalPages }} 页</span>
                    <button class="pagination-btn" @click="changePage(currentPage + 1)" :disabled="currentPage === totalPages">
                        下一页
                    </button>
                </div>

            </div>
        </section>
        <!--News One End-->

        <!--Site Footer Start-->
        <footer class="site-footer">
            <div class="site-footer-bg" style="background-image: url(static/image/site-footer-bg.png);">
            </div>
            <div class="container">
                <div class="site-footer__top">
                    <div class="row">
<!--                        <div class="col-xl-4 col-lg-8 col-md-8 wow fadeInUp" data-wow-delay="100ms">-->
<!--                            <div class="footer-widget__column footer-widget__about">-->
<!--                                <div class="footer-widget__logo">-->
<!--                                    <a><img :src="companyInfo.logoImg" alt="" style="height: 34px"></a>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
                        <div class="col-xl-4 col-lg-8 col-md-8 wow fadeInUp" data-wow-delay="200ms">
                            <div class="footer-widget__column footer-widget__contact clearfix">
                                <h3 class="footer-widget__title">公司地址</h3>
                                <ul class="footer-widget__contact-list list-unstyled clearfix">
                                    <li>
                                        <div class="icon">
                                            <span class="icon-pin"></span>
                                        </div>
                                        <div class="text">
                                            <p>{{companyInfo.address}}</p>
                                        </div>
                                    </li>
                                </ul>
                                <div class="footer-widget__open-hour">
                                    <h3 class="footer-widget__open-hour-title">工作时间</h3>
                                    <h3 class="footer-widget__open-hour-text">工作日 早上9:00-18:00</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-4 col-lg-8 col-md-8 wow fadeInUp" data-wow-delay="400ms">
                            <div class="footer-widget__column footer-widget__newsletter">
                                <h3 class="footer-widget__title">联系电话</h3>
                                <div class="footer-widget__phone">
                                    <div class="footer-widget__phone-icon">
                                        <span class="icon-telephone"></span>
                                    </div>
                                    <div class="footer-widget__phone-text">
                                        <a href="tel:9200368090">{{companyInfo.phone}}</a>
                                        <p>欢迎拨打</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="site-footer__bottom">
                    <div class="row">
                        <div class="col-xl-12">
                            <div class="site-footer__bottom-inner">
                                <p class="site-footer__bottom-text">{{companyInfo.copyright}} <a target="_blank"
                                        href="https://beian.miit.gov.cn/#/Integrated/index">{{companyInfo.icpNumber}}</a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
        <!--Site Footer End-->


    </div><!-- /.page-wrapper -->


    <div class="mobile-nav__wrapper" :class="{expanded: expanded}">
        <div class="mobile-nav__overlay mobile-nav__toggler"></div>
        <!-- /.mobile-nav__overlay -->
        <div class="mobile-nav__content">
            <span class="mobile-nav__close mobile-nav__toggler" @click="expanded = false"><i class="fa fa-times"></i></span>

            <div class="logo-box">
                <a aria-label="logo image"><img src="static/picture/logo-2.png" width="143"
                        alt=""></a>
            </div>
            <!-- /.logo-box -->
            <div class="mobile-nav__container"></div>
            <!-- /.mobile-nav__container -->



        </div>
        <!-- /.mobile-nav__content -->
    </div>
    </div>
    <!-- /.mobile-nav__wrapper -->

    <div class="search-popup">
        <div class="search-popup__overlay search-toggler"></div>
        <!-- /.search-popup__overlay -->
        <div class="search-popup__content">
            <form action="#">
                <label for="search" class="sr-only">search here</label><!-- /.sr-only -->
                <input type="text" id="search" placeholder="Search Here...">
                <button type="submit" aria-label="search submit" class="thm-btn">
                    <i class="icon-magnifying-glass"></i>
                </button>
            </form>
        </div>
        <!-- /.search-popup__content -->
    </div>
    <!-- /.search-popup -->

    <a href="#" data-target="html" class="scroll-to-target scroll-to-top"><i class="fa fa-angle-up"></i></a>


    <script src="static/js/jquery-3.6.0.min.js"></script>
    <script src="static/js/bootstrap.bundle.min.js"></script>
    <script src="static/js/jarallax.min.js"></script>
    <script src="static/js/jquery.ajaxchimp.min.js"></script>
    <script src="static/js/jquery.appear.min.js"></script>
    <script src="static/js/jquery.circle-progress.min.js"></script>
    <script src="static/js/jquery.magnific-popup.min.js"></script>
    <script src="static/js/jquery.validate.min.js"></script>
    <script src="static/js/nouislider.min.js"></script>
    <script src="static/js/odometer.min.js"></script>
    <script src="static/js/swiper.min.js"></script>
    <script src="static/js/tiny-slider.min.js"></script>
    <script src="static/js/wNumb.min.js"></script>
    <script src="static/js/wow.js"></script>
    <script src="static/js/isotope.js"></script>
    <script src="static/js/countdown.min.js"></script>
    <script src="static/js/owl.carousel.min.js"></script>
    <script src="static/js/jquery.bxslider.min.js"></script>
    <script src="static/js/bootstrap-select.min.js"></script>
    <script src="static/js/vegas.min.js"></script>
    <script src="static/js/jquery-ui.js"></script>
    <script src="static/js/timePicker.js"></script>
    <script src="static/js/jquery.circleType.js"></script>
    <script src="static/js/jquery.lettering.min.js"></script>




    <!-- template js -->
    <script src="static/js/insur.js"></script>

    <script src="static/js/vue.js"></script>
    <script src="static/js/api-config.js"></script>
    <script src="static/js/auth-components.js"></script>
    <script>
        new Vue({
            el: '#app',
            mixins: [AuthMixin],
            data: {
                expanded: false,
                bannerList: [],
                newsList: [],
                businessPartner: [],
                recommendList: [],
                companyInfo: {},
                keyword: '',
                currentPage: 1,
                pageSize: 15,
                totalPages: 0
            },
            computed: {
                // Removed filteredNewsList and paginatedNews computed properties
                // totalPages is now updated in getNews based on API response
            },
            mounted() {
                // this.getBanner()
                this.getNews()
                this.getBusinessPartner()
                this.getRecommend()
                this.getCompanyInfo()
            },
            methods: {
                // 通用方法：检查用户是否已登录
                isUserLoggedIn() {
                    const token = localStorage.getItem('X-Access-Token');
                    const userInfo = localStorage.getItem('userInfo');
                    return !!(token && userInfo);
                },

                // 通用方法：获取请求头（包含token）
                getAuthHeaders() {
                    const headers = {
                        'Content-Type': 'application/json'
                    };

                    const token = localStorage.getItem('X-Access-Token');
                    if (token) {
                        headers['X-Access-Token'] = token;
                    }

                    return headers;
                },

                // 保存用户浏览记录
                async saveUserRecord(newsItem) {
                    if (!this.isUserLoggedIn()) {
                        console.log('用户未登录，跳过保存浏览记录');
                        return;
                    }

                    try {
                        console.log('💾 保存用户浏览记录:', newsItem.name);

                        const response = await fetch(`${jeeApi}/jeecg-boot/wh/record/saveRecord`, {
                            method: 'POST',
                            headers: this.getAuthHeaders(),
                            body: JSON.stringify({
                                recordType: 1,        // 记录类型：1
                                contentType: 2,       // 内容类型：2
                                contentId: newsItem.id // 列表的id
                            })
                        });

                        const data = await response.json();
                        if (data.success) {
                            console.log('✅ 浏览记录保存成功');
                        } else {
                            console.log('❌ 浏览记录保存失败:', data.message);
                        }
                    } catch (error) {
                        console.error('❌ 保存浏览记录出错:', error);
                    }
                },

                // 获取轮播
                async getBanner() {
                    const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/whCarousel/list', {
                        method: 'get',
                        headers: this.getAuthHeaders()
                    })
                    const data = await response.json();
                    this.bannerList = data.result.records
                    this.$nextTick(() => {
                        window.thmSwiperInit();
                    })

                },
                async toDetail(item) {
                    console.log('🔗 用户点击资讯:', item.name);

                    // 保存用户浏览记录（仅登录用户）
                    await this.saveUserRecord(item);

                    // 原有的点击统计
                    try {
                        await fetch(jeeApi + '/jeecg-boot/firmInformation/whClick/addOne?clicksId='+item.clicksId, {
                            method: 'get',
                            headers: this.getAuthHeaders()
                        });
                    } catch (error) {
                        console.error('点击统计失败:', error);
                    }

                    // 跳转到详情页
                    window.location.href = `news-details.html?id=${item.id}`;
                },
                // 获取新闻 (Updated for API pagination)
                async getNews() {
                    try {
                        // Remove keyword from URL, it will be in the body
                        const url = `${jeeApi}/jeecg-boot/api/wechat/appNews/list/${this.currentPage}/${this.pageSize}`;
                        const response = await fetch(url, {
                            method: 'POST', // Ensure method is POST
                            headers: this.getAuthHeaders(), // 使用带token的请求头
                            // Add the request body with the search keyword
                            body: JSON.stringify({ name: this.keyword || '' }) // Send keyword in body, default to empty string if null/undefined
                        });
                        const data = await response.json();
                        if (data && data.success && data.result) {
                            this.newsList = data.result.records || []; // Use records from API
                            this.totalPages = Math.ceil((data.result.total || 0) / this.pageSize); // Calculate totalPages from API total
                            // Ensure currentPage is valid after fetch/search
                            if (this.currentPage > this.totalPages && this.totalPages > 0) {
                                this.currentPage = this.totalPages;
                                // Optionally re-fetch if currentPage was adjusted, though often just showing the last page is fine
                            } else if (this.totalPages === 0) {
                                this.currentPage = 1; // Reset to 1 if no results
                            }
                        } else {
                            console.error("Failed to fetch news or invalid data format:", data);
                            this.newsList = [];
                            this.totalPages = 0;
                            this.currentPage = 1;
                        }
                    } catch (error) {
                        console.error("Error fetching news:", error);
                        this.newsList = [];
                        this.totalPages = 0;
                        this.currentPage = 1;
                    }
                },
                // 合作伙伴
                async getBusinessPartner() {
                    const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/firmInformation/list', {
                        method: 'get',
                        headers: this.getAuthHeaders()
                    })
                    const data = await response.json();
                    this.businessPartner = data.result.records

                },
                // 推荐
                async getRecommend() {
                    const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/whProducts/list', {
                        method: 'get',
                        headers: this.getAuthHeaders()
                    })
                    const data = await response.json();
                    this.recommendList = data.result.records

                },
                // 推荐
                async getCompanyInfo() {
                    const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/whHome/getOne', {
                        method: 'get',
                        headers: this.getAuthHeaders()
                    })
                    const data = await response.json();
                    this.companyInfo = data.result
                    document.title =  this.companyInfo.name

                },
                changePage(page) {
                    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
                        this.currentPage = page;
                        this.getNews(); // Fetch data for the new page
                    }
                }
            }
        })
    </script>

    <!-- 登录弹框 -->
    <login-modal
        :show="showLoginPopup"
        @close="closeLoginPopup"
        @login-success="onLoginSuccess">
    </login-modal>

    <!-- 退出登录确认弹框 -->
    <logout-modal
        :show="showLogoutPopup"
        @close="closeLogoutPopup"
        @confirm="handleLogout">
    </logout-modal>
</body>

</html>

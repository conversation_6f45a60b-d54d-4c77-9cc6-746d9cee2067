/* 认证组件统一样式 */

/* 用户菜单弹框样式 */
.user-menu-popup {
    position: absolute;
    top: 100%;
    right: 0;
    background: #fff;
    border: 1px solid #eee;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    min-width: 150px;
    z-index: 1000;
    visibility: hidden;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    overflow: hidden; /* 防止内容溢出 */
}

.user-menu-popup.active {
    visibility: visible;
    opacity: 1;
    transform: translateY(0);
}

.user-menu-popup__item {
    border-bottom: 1px solid #f5f5f5;
    position: relative; /* 确保正确定位 */
}

.user-menu-popup__item:last-child {
    border-bottom: none;
}

.user-menu-popup__item:first-child {
    border-top: none; /* 确保第一个项目没有上边框 */
}

.user-menu-popup__link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #333;
    text-decoration: none;
    transition: background-color 0.3s ease;
    width: 100%;
    box-sizing: border-box;
}

.user-menu-popup__link:hover {
    background-color: #f8f9fa;
    color: #015fc9;
}

.user-menu-popup__link i {
    margin-right: 10px;
    width: 16px;
    text-align: center;
}

/* 确保菜单容器的唯一性 */
.main-menu__user-container .user-menu-popup {
    position: absolute !important;
    top: 100% !important;
    right: 0 !important;
}

/* 防止重复显示 */
.user-menu-popup * {
    pointer-events: auto;
}

/* 确保主菜单的唯一性 */
#main-user-menu {
    display: block !important;
}

/* 隐藏可能的重复菜单 */
.user-menu-popup:not(#main-user-menu) {
    display: none !important;
}

/* 导航栏右侧用户区域样式 */
.main-menu__right {
    margin-left: auto;
    display: flex;
    align-items: center;
}

.main-menu__wrapper-inner {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.main-menu__user {
    color: #333;
    font-size: 24px;
    padding: 10px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.main-menu__user:hover {
    color: #015fc9;
    background-color: rgba(1, 95, 201, 0.1);
}

.main-menu__user.logged-in {
    color: #015fc9;
}

/* 登录弹框样式 */
.login-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    visibility: hidden;
    opacity: 0;
    transition: all 0.3s ease;
}

.login-popup.active {
    visibility: visible;
    opacity: 1;
}

.login-popup .search-popup__content {
    background: #fff;
    border-radius: 8px;
    padding: 30px;
    max-width: 400px;
    width: 90%;
    position: relative;
}

.login-popup__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.login-popup__header h3 {
    margin: 0;
    color: #333;
    font-size: 20px;
}

.login-popup__close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #666;
    padding: 5px;
}

.login-popup__close:hover {
    color: #333;
}

.login-popup__form-group {
    margin-bottom: 20px;
}

.login-popup__form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.login-popup__form-group input:focus {
    outline: none;
    border-color: #015fc9;
}

.captcha-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

.captcha-group input {
    flex: 1;
}

.captcha-image {
    height: 40px;
    cursor: pointer;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.login-popup__submit {
    width: 100%;
    padding: 12px;
    background-color: #015fc9;
    color: #fff;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.login-popup__submit:hover:not(:disabled) {
    background-color: #014ba8;
}

.login-popup__submit:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

/* 退出登录确认弹框样式 */
.logout-confirm-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    visibility: hidden;
    opacity: 0;
    transition: all 0.3s ease;
}

.logout-confirm-popup.active {
    visibility: visible;
    opacity: 1;
}

.logout-confirm-popup .search-popup__content {
    background: #fff;
    border-radius: 8px;
    padding: 30px;
    max-width: 400px;
    width: 90%;
}

.logout-confirm__content h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 18px;
    text-align: center;
}

.logout-confirm__content p {
    margin: 0 0 25px 0;
    color: #666;
    text-align: center;
    line-height: 1.5;
}

.logout-confirm__buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.logout-confirm__btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    min-width: 80px;
}

.logout-confirm__btn--cancel {
    background-color: #6c757d;
    color: #fff;
}

.logout-confirm__btn--cancel:hover {
    background-color: #5a6268;
}

.logout-confirm__btn--confirm {
    background-color: #dc3545;
    color: #fff;
}

.logout-confirm__btn--confirm:hover {
    background-color: #c82333;
}

/* 响应式样式 */
@media (max-width: 991px) {
    .main-menu__right {
        margin-left: 15px;
    }
}

@media (max-width: 768px) {
    .main-menu__wrapper-inner {
        flex-wrap: wrap;
    }
    
    .main-menu__right {
        margin-left: auto;
        margin-top: 10px;
    }
    
    .main-menu__user {
        font-size: 20px;
        padding: 8px;
    }

    .user-menu-popup {
        right: -10px;
        min-width: 140px;
    }

    .logout-confirm__buttons {
        flex-direction: column;
        align-items: center;
    }

    .logout-confirm__btn {
        width: 100%;
        max-width: 200px;
    }
}

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{companyInfo.name}}</title>
    <!-- favicons Icons -->




    <meta name="description" content="Insur HTML 5 Template ">

    <!-- fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com/">

    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">

    <link href="static/css/css2.css" rel="stylesheet">


    <link rel="stylesheet" href="static/css/bootstrap.min.css">
    <link rel="stylesheet" href="static/css/animate.min.css">
    <link rel="stylesheet" href="static/css/custom-animate.css">
    <link rel="stylesheet" href="static/css/all.min.css">
    <link rel="stylesheet" href="static/css/jarallax.css">
    <link rel="stylesheet" href="static/css/swiper.min.css">
    <link rel="stylesheet" href="static/css/style.css">
    <link rel="stylesheet" href="static/css/style1.css">
    <link rel="stylesheet" href="static/css/timePicker.css">
    <link rel="stylesheet" href="static/css/jquery-ui.css">
    <link rel="stylesheet" href="static/css/vegas.min.css">
    <!-- template styles -->
    <link rel="stylesheet" href="static/css/insur.css">
    <link rel="stylesheet" href="static/css/insur-responsive.css">
    <link rel="stylesheet" href="static/css/custom-carousel.css">

    <!-- 添加新闻列表样式 -->
    <style>
        /* 用户头像图标样式 */
        .main-menu__user {
            display: inline-block;
            margin-left: 15px;
            font-size: 24px;
            color: #333;
            transition: color 0.3s ease;
            text-decoration: none !important;
        }

        .main-menu__user:hover {
            color: #ff6600;
            text-decoration: none !important;
        }

        .main-menu__user.logged-in {
            color: #ff6600;
        }

        .main-menu__user:focus,
        .main-menu__user:active {
            outline: none;
            text-decoration: none !important;
            background: none !important;
        }

        /* 登录弹框样式 */
        .login-popup {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
            visibility: hidden;
            opacity: 0;
            transition: all 0.3s ease;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-popup.active {
            visibility: visible;
            opacity: 1;
        }

        .login-popup .search-popup__content {
            background: #fff;
            border-radius: 8px;
            padding: 30px;
            max-width: 400px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
        }

        .login-popup__header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .login-popup__header h3 {
            margin: 0;
            font-size: 24px;
            color: #333;
        }

        .login-popup__close {
            background: none;
            border: none;
            font-size: 20px;
            color: #999;
            cursor: pointer;
            padding: 5px;
        }

        .login-popup__close:hover {
            color: #333;
        }

        /* 重置登录表单样式 */
        .login-popup__form {
            display: flex;
            flex-direction: column;
        }

        .login-popup__form .comment-form__input-box {
            margin-bottom: 20px;
            order: initial;
        }

        .login-popup__form .comment-form__btn-box {
            order: 999;
            margin-top: 10px;
        }

        .login-popup__phone-input {
            order: 1;
        }

        .login-popup__captcha-input {
            order: 2;
        }

        .login-popup__form input[type="tel"],
        .login-popup__form input[type="text"] {
            width: 100% !important;
            padding: 12px 15px !important;
            border: 1px solid #ddd !important;
            border-radius: 5px !important;
            font-size: 16px !important;
            box-sizing: border-box !important;
            background: #fff !important;
            color: #333 !important;
            height: 50px !important;
            display: block !important;
        }

        .login-popup__form input[type="tel"]:focus,
        .login-popup__form input[type="text"]:focus {
            outline: none !important;
            border-color: #015fc9 !important;
        }

        .login-popup__sms-row {
            display: flex;
            gap: 10px;
            align-items: stretch;
        }

        .login-popup__sms-input {
            flex: 1;
        }

        .login-popup__sms-input input {
            width: 100% !important;
        }

        .login-popup__sms-btn {
            width: 120px !important;
            height: 50px !important;
            font-size: 14px !important;
            white-space: nowrap !important;
            flex-shrink: 0 !important;
            padding: 0 10px !important;
        }

        .login-popup__form .comment-form__btn {
            width: 100% !important;
            height: 50px !important;
            font-size: 16px !important;
        }

        /* 用户菜单弹框样式 */
        .user-menu-popup {
            position: absolute;
            top: 100%;
            right: 0;
            background: #fff;
            border: 1px solid #eee;
            border-radius: 5px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            min-width: 150px;
            z-index: 1000;
            visibility: hidden;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            overflow: hidden; /* 防止内容溢出 */
        }

        .user-menu-popup.active {
            visibility: visible;
            opacity: 1;
            transform: translateY(0);
        }

        .user-menu-popup__item {
            border-bottom: 1px solid #f5f5f5;
            position: relative; /* 确保正确定位 */
        }

        .user-menu-popup__item:last-child {
            border-bottom: none;
        }

        .user-menu-popup__item:first-child {
            border-top: none; /* 确保第一个项目没有上边框 */
        }

        .user-menu-popup__link {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #333;
            text-decoration: none;
            transition: background-color 0.3s ease;
            width: 100%;
            box-sizing: border-box;
        }

        .user-menu-popup__link:hover {
            background-color: #f8f9fa;
            color: #015fc9;
        }

        .user-menu-popup__link i {
            margin-right: 10px;
            width: 16px;
            text-align: center;
        }

        /* 确保菜单容器的唯一性 */
        .main-menu__user-container .user-menu-popup {
            position: absolute !important;
            top: 100% !important;
            right: 0 !important;
        }

        /* 防止重复显示 */
        .user-menu-popup * {
            pointer-events: auto;
        }

        /* 确保主菜单的唯一性 */
        #main-user-menu {
            display: block !important;
        }

        /* 隐藏可能的重复菜单 */
        .user-menu-popup:not(#main-user-menu) {
            display: none !important;
        }

        /* 隐藏sticky header中的用户头像和菜单 */
        .stricky-header .main-menu__right,
        .stricky-header .main-menu__user-container,
        .stricky-header .user-menu-popup {
            display: none !important;
        }

        /* 退出登录确认弹框样式 */
        .logout-confirm-popup {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
            visibility: hidden;
            opacity: 0;
            transition: all 0.3s ease;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logout-confirm-popup.active {
            visibility: visible;
            opacity: 1;
        }

        .logout-confirm-popup .search-popup__content {
            background: #fff;
            border-radius: 8px;
            padding: 30px;
            max-width: 400px;
            width: 90%;
            position: relative;
        }

        .logout-confirm__content {
            text-align: center;
            padding: 20px;
        }

        .logout-confirm__content h3 {
            margin-bottom: 15px;
            font-size: 20px;
            color: #333;
        }

        .logout-confirm__content p {
            margin-bottom: 25px;
            color: #666;
            font-size: 16px;
        }

        .logout-confirm__buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .logout-confirm__btn {
            min-width: 100px;
            padding: 10px 20px;
        }

        .logout-confirm__btn--cancel {
            background-color: #6c757d;
        }

        .logout-confirm__btn--cancel:hover {
            background-color: #5a6268;
        }

        /* 导航栏右侧用户区域样式 */
        .main-menu__right {
            margin-left: auto;
            display: flex;
            align-items: center;
        }

        .main-menu__wrapper-inner {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .main-menu__user {
            color: #333;
            font-size: 24px;
            padding: 10px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .main-menu__user:hover {
            color: #015fc9;
            background-color: rgba(1, 95, 201, 0.1);
        }

        .main-menu__user.logged-in {
            color: #015fc9;
        }

        /* 响应式样式 */
        @media (max-width: 991px) {
            .main-menu__right {
                margin-left: 15px;
            }
        }

        @media (max-width: 768px) {
            .main-menu__wrapper-inner {
                flex-wrap: wrap;
            }

            .main-menu__right {
                margin-left: auto;
                margin-top: 10px;
            }

            .main-menu__user {
                font-size: 20px;
                padding: 8px;
            }

            .user-menu-popup {
                right: -10px;
                min-width: 140px;
            }

            .logout-confirm__buttons {
                flex-direction: column;
                align-items: center;
            }

            .logout-confirm__btn {
                width: 100%;
                max-width: 200px;
            }
        }

        /* News Tabs - Design Master Polish */
        .news-list-tabs {
            margin-bottom: 25px;
        }

        .news-list-tabs__nav {
            display: flex;
            padding-bottom: 5px;
            margin-bottom: 35px; /* Slightly more space below tabs */
            overflow-x: auto;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .news-list-tabs__nav::-webkit-scrollbar {
            display: none;
        }

        .news-list-tabs__item {
            padding: 8px 20px; /* Refined padding */
            cursor: pointer;
            border-radius: 20px;
            font-size: 16px;
            font-weight: 500;
            color: #666; /* Slightly darker grey for inactive text */
            background-color: transparent;
            transition: all 0.2s ease-in-out; /* Faster transition */
            white-space: nowrap;
            margin-right: 8px; /* Slightly reduce space between tabs */
            border: 1px solid transparent;
        }

        .news-list-tabs__item:hover {
           color: #015fc9;
        }

        .news-list-tabs__item.active {
            background-color: #015fc9;
            color: #ffffff;
            border-color: #015fc9;
        }
        .news-list-tabs__item.active:hover {
           color: #ffffff;
        }

        /* News List Content - Design Master Polish */
        .news-list-content {
            display: none;
        }

        .news-list-content.active {
            display: block;
        }



        .news-list__item {
            padding: 20px 0; /* Increased padding */
            border-bottom: 1px solid #f0f0f0; /* Very light border */
            cursor: pointer;
            transition: background-color 0.2s ease-in-out;
        }

        .news-list__item:last-child {
            border-bottom: none;
        }

        .news-list__item:hover {
            background-color: #f5f5f5; /* Slightly darker background */
            transform: scale(0.99); /* Slightly scale down for sinking effect */
            box-shadow: inset 0 1px 4px rgba(0, 0, 0, 0.1); /* Inset shadow for pressed look */
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out, background-color 0.2s ease-in-out; /* Include transform in transition */
        }

        .news-list__item > div:first-of-type {
            margin-bottom: 10px; /* Increased space */
        }

        .news-list__category {
            display: inline-block;
            background-color: #eaf2fc; /* Light blue background like image */
            color: #3c77c9; /* Darker blue text like image */
            padding: 4px 10px; /* Match image padding */
            border-radius: 4px;
            font-size: 13px; /* Slightly larger tag text */
            font-weight: 500;
            margin-right: 12px; /* More space after tag */
            white-space: nowrap;
            vertical-align: middle;
        }

        .news-list__title {
            display: inline;
            font-size: 18px;
            font-weight: 600; /* Bolder title */
            color: #333;
            overflow: hidden;
            text-overflow: ellipsis;
            vertical-align: middle;
            line-height: 1.5; /* Adjust line height */
        }
        .news-list__item:hover .news-list__title {
            color: #015fc9;
        }

        .news-list__meta {
            align-items: center;
            color: #999; /* Lighter grey for meta */
            font-size: 13px; /* Smaller meta font */
        }

        .news-list__meta i {
            margin-right: 6px; /* Adjust icon spacing */
            vertical-align: middle;
            font-size: 14px; /* Ensure icon size is consistent */
        }

        .news-list__date {
            margin-right: 25px; /* More space between date and views */
        }

        /* Responsive Adjustments - Design Master Polish */
        @media (max-width: 767px) {
            .news-list-tabs__nav {
                justify-content: flex-start;
                padding-bottom: 5px;
                margin-bottom: 25px; /* Adjust spacing */
            }

            .news-list-tabs__item {
                padding: 6px 14px; /* Adjust padding */
                font-size: 15px;
                margin-right: 5px;
            }

            .news-list__item {
                padding: 15px 0;
                flex-wrap: wrap;
            }

            .news-list__item > div:first-of-type {
                margin-bottom: 8px; /* Adjust space */
            }

            .news-list__category {
                font-size: 12px;
                padding: 3px 8px;
                margin-right: 8px;
            }

            .news-list__title {
                font-size: 16px;
                font-weight: 600;
                white-space: normal;
                display: inline;
                line-height: 1.45;
            }

            .news-list__meta {
                justify-content: flex-start;
                font-size: 12px; /* Smaller meta on mobile */
                color: #aaa; /* Even lighter meta on mobile */
            }
             .news-list__date {
                margin-right: 15px; /* Adjust spacing */
             }
             .news-list__meta i {
                 font-size: 13px; /* Adjust icon size */
             }
        }
    </style>
</head>

<body class="custom-cursor">

    <div class="custom-cursor__cursor"></div>
    <div class="custom-cursor__cursor-two"></div>





    <div class="preloader">
        <div class="preloader__image"></div>
    </div>
    <!-- /.preloader -->
    <div id="app">
        <div class="page-wrapper">
            <header class="main-header clearfix">
                <div class="main-header__top">
                    <div class="container">
                        <div class="main-header__top-inner">
                            <div class="main-header__top-address">
                                <ul class="list-unstyled main-header__top-address-list">
                                    <li>
                                        <i class="icon">
                                            <span class="icon-pin"></span>
                                        </i>
                                        <div class="text">
                                            <p>{{companyInfo.address}}</p>
                                        </div>
                                    </li>
                                    <li>
                                        <i class="icon">
                                            <span class="icon-email"></span>
                                        </i>
                                        <div class="text">
                                            <p><a href="mailto:<EMAIL>">{{companyInfo.mailbox}}</a></p>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <nav class="main-menu clearfix">
                    <div class="main-menu__wrapper clearfix">
                        <div class="container">
                            <div class="main-menu__wrapper-inner clearfix">
                                <div class="main-menu__left">
                                    <div class="main-menu__logo">
                                        <a><img :src="companyInfo.logoImg" alt="" style="height: 27px"></a>
                                    </div>
                                    <div class="main-menu__main-menu-box">
                                        <div class="main-menu__main-menu-box-inner">
                                            <a href="#" class="mobile-nav__toggler" @click="expanded = true"><i class="fa fa-bars"></i></a>
                                            <ul class="main-menu__list one-page-scroll-menu">
                                                <li class=" current megamenu scrollToLink">
                                                    <a href="index-one-page.html">首页 </a>
                                                </li>

                                                <li class="scrollToLink">
                                                    <a href="company.html">保险公司 </a>
                                                </li>
                                                <li class="scrollToLink">
                                                    <a href="products.html">保险产品</a>
                                                </li>
                                                <li class="scrollToLink">
                                                    <a href="news.html">新闻资讯</a>
                                                </li>
                                                <li class="scrollToLink" style="margin-right: 37px;">
                                                    <a href="about.html">关于我们</a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <!-- 右侧用户头像区域 -->
                                <div class="main-menu__right">
                                    <div class="main-menu__user-container" style="position: relative; display: inline-block;">
                                        <a href="#" class="main-menu__user user-toggler" :class="{ 'logged-in': isLoggedIn }" @click.prevent="handleUserClick" title="用户中心">
                                            <i class="fa fa-user-circle"></i>
                                        </a>
                                        <!-- 用户菜单弹框 -->
                                        <div class="user-menu-popup main-header-user-menu" :class="{ active: showUserMenu }" v-if="isLoggedIn" id="main-user-menu">
                                            <div class="user-menu-popup__item">
                                                <a href="news-sidebar.html" class="user-menu-popup__link">
                                                    <i class="fa fa-user"></i>
                                                    <span>个人中心</span>
                                                </a>
                                            </div>
                                            <div class="user-menu-popup__item">
                                                <a href="#" @click.prevent="showLogoutConfirm" class="user-menu-popup__link">
                                                    <i class="fa fa-sign-out-alt"></i>
                                                    <span>退出登录</span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </nav>
            </header>

            <div class="stricky-header stricked-menu main-menu">
                <div class="sticky-header__content"></div><!-- /.sticky-header__content -->
            </div><!-- /.stricky-header -->

            <!--Main Slider Start-->
            <section class="main-slider clearfix" id="home">
                <div class="swiper-container thm-swiper__slider" style="width: 1200px" data-swiper-options='{"slidesPerView": 1, "loop": true,
                "effect": "fade",
                "pagination": {
                "el": "#main-slider-pagination",
                "type": "bullets",
                "clickable": true
                },
                "navigation": {
                "nextEl": "#main-slider__swiper-button-next",
                "prevEl": "#main-slider__swiper-button-prev"
                },
                "autoplay": {
                "delay": 5000
                }}'>
                    <div class="swiper-wrapper" v-if="bannerList.length" style="height: 600px">

                        <div class="swiper-slide" v-for="(item,index) in bannerList" :key="index">
                            <div class="image-layer" :style="{'background-image': `url(${item.img})`}"></div>
                            <!-- /.image-layer -->

                            <!-- <div class="main-slider-shape-1 float-bob-x">
                                <img src="static/picture/main-slider-shape-1.png" alt="">
                            </div> -->

                            <div class="container">
                                <div class="row">
                                    <div class="col-xl-12">
                                        <div class="main-slider__content">
                                            <h2 class="main-slider__title">{{item.name}}</h2>
                                            <!-- <p class="main-slider__text">Phasellus condimentum laoreet turpis, ut tincid
                                            sodales <br> in. Integer leo arcu, mollis sit amet tempor.</p>
                                        <div class="main-slider__btn-box">
                                            <a href="about.html" class="thm-btn main-slider__btn">Let's Get Started</a>
                                        </div> -->
                                        <div class="main-slider__btn-box" v-if="item.carouselUrl">
											<a @click="handleJump(item.carouselUrl,item)" class="thm-btn main-slider__btn">查看详情</a>
										</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- If we need navigation buttons -->
                    <div class="main-slider__nav">
                        <div class="swiper-button-prev" id="main-slider__swiper-button-prev">
                            <i class="fa fa-arrow-left"></i>
                        </div>
                        <div class="swiper-button-next" id="main-slider__swiper-button-next">
                            <i class="fa fa-arrow-right"></i>
                        </div>
                    </div>

                    <!-- 轮播图指示器 -->
                    <div id="main-slider-pagination" class="swiper-pagination"></div>

                </div>
            </section>
            <!--Main Slider End-->

            <!--Feature One Start-->
            <section class="feature-one">
                <div class="container">
                    <div class="feature-one__inner">
                        <div class="row">
                            <!--Feature One Single Start-->
                            <div class="col-xl-4 col-lg-4 wow fadeInUp"  data-wow-delay="100ms">
                                <div class="feature-one__single">
                                    <div class="feature-one__single-inner">
                                        <div class="feature-one__icon">
                                            <span class="icon-insurance"></span>
                                        </div>
                                        <div class="feature-one__count"></div>
                                        <div class="feature-one__shape">
                                            <img src="static/picture/feature-one-shape-1.png" alt="">
                                        </div>
                                        <h3 class="feature-one__title"><a href="about.html">{{sortList.numOneTop}}</a></h3>
                                        <p class="feature-one__text">{{sortList.numOne}}</p>
                                    </div>
                                </div>
                            </div>
                            <!--Feature One Single End-->
                            <!--Feature One Single Start-->
                            <div class="col-xl-4 col-lg-4 wow fadeInUp" data-wow-delay="200ms">
                                <div class="feature-one__single">
                                    <div class="feature-one__single-inner">
                                        <div class="feature-one__icon">
                                            <span class="icon-cashback"></span>
                                        </div>
                                        <div class="feature-one__count"></div>
                                        <div class="feature-one__shape">
                                            <img src="static/picture/feature-one-shape-1.png" alt="">
                                        </div>
                                        <h3 class="feature-one__title"><a href="about.html">{{sortList.numTwoTop}}</a></h3>
                                        <p class="feature-one__text">{{sortList.numTwo}}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-4 col-lg-4 wow fadeInUp" data-wow-delay="300ms">
                                <div class="feature-one__single">
                                    <div class="feature-one__single-inner">
                                        <div class="feature-one__icon">
                                            <span class="icon-house"></span>
                                        </div>
                                        <div class="feature-one__count"></div>
                                        <div class="feature-one__shape">
                                            <img src="static/picture/feature-one-shape-1.png" alt="">
                                        </div>
                                        <h3 class="feature-one__title"><a href="about.html">{{sortList.numThreeTop}}</a></h3>
                                        <p class="feature-one__text">{{sortList.numThree}}</p>
                                    </div>
                                </div>
                            </div>
                            <!--Feature One Single End-->
                        </div>
                    </div>
                </div>
            </section>
            <!--Feature One End-->


            <!--About One Start-->
            <section class="about-one">
                <div class="about-one-bg wow slideInRight" data-wow-delay="100ms" data-wow-duration="2500ms"
                    style="background-image: url(assets/images/backgrounds/about-one-bg.png);"></div>
                <div class="container">
                    <div class="row">
                        <div class="col-xl-6">
                            <div class="about-one__left">
                                <div class="about-one__img-box wow slideInLeft" data-wow-delay="100ms"
                                    data-wow-duration="2500ms">
                                    <div class="about-one__img">
                                        <img :src="companyInfo.moveImg" alt="">
                                    </div>
                                    <div class="about-one__img-two">
                                        <img style="height: 350px; " :src="companyInfo.homeImg" alt="">
                                    </div>
                                    <div class="about-one__experience">
                                        <h2 class="about-one__experience-year">10</h2>
                                        <p class="about-one__experience-text">多年 <br> 专业服务</p>
                                    </div>
                                    <div class="about-one__shape-1">
                                        <img src="static/picture/about-one-shape-1.jpg" alt="">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-6">
                            <div class="about-one__right" style="margin-left: 40px;">
                                <div class="section-title text-left">
                                    <div class="section-sub-title-box" style="margin-left: 40px;">
                                        <p class="section-sub-title">关于{{companyInfo.companyName}}</p>
                                        <div class="section-title-shape-1">
                                            <img src="static/picture/section-title-shape-1.png" alt="">
                                        </div>
                                        <div class="section-title-shape-2">
                                            <img src="static/picture/section-title-shape-2.png" alt="">
                                        </div>
                                    </div>
                                    <h2 class="section-title__title">{{companyInfo.name}}简介</h2>
                                </div>
                                <p class="about-one__text-1">更专业的文化传播公司</p>
                                <ul class="list-unstyled about-one__points">
                                    <li>
                                        <div class="icon">
                                            <i class="fa fa-check"></i>
                                        </div>
                                        <div class="text">
                                            <p>更优惠点价格</p>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="icon">
                                            <i class="fa fa-check"></i>
                                        </div>
                                        <div class="text">
                                            <p>更专业的服务</p>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="icon">
                                            <i class="fa fa-check"></i>
                                        </div>
                                        <div class="text">
                                            <p>更全面的保障</p>
                                        </div>
                                    </li>
                                </ul>
                                <p class="about-one__text-2" v-html="companyInfo.introduction"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!--About One End-->

            <!--About One End-->

            <!--Services One Start-->
            <section class="services-one">
                <div class="services-one__top">
                    <div class="container">
                        <div class="section-title text-center">
                            <div class="section-sub-title-box" style="margin-left: 0;">
                                <p class="section-sub-title">合作机构</p>
                                <div class="section-title-shape-1">
                                    <img src="static/picture/section-title-shape-1.png" alt="">
                                </div>
                                <div class="section-title-shape-2">
                                    <img src="static/picture/section-title-shape-2.png" alt="">
                                </div>
                            </div>
                            <h2 class="section-title__title">合作机构</h2>
                        </div>
                    </div>
                </div>
                <div class="services-one__bottom">
                    <div class="services-one__container">
                        <div class="row">
                            <!--Services One Single Start-->
                            <div class="col-xl-3 col-lg-4 col-md-6 wow fadeInUp" data-wow-delay="100ms"
                                v-for="item in businessPartner" :key="item" @click="handleCompanyClick(item)" style="cursor: pointer;">
                                <div class="services-one__single">
                                    <div class="service-one__img">
                                        <img :src="item.companyLogo" alt="">
                                    </div>
                                    <div class="service-one__content">
                                        <h2 class="service-one__title" style="height: 60px">{{item.name}}</h2>
                                        <p class="service-one__text">成立时间：{{item.regTime}}</p>
                                        <p class="service-one__text"><i
                                            class="far fa-eye" style="color: #015fc9"></i> {{item.clickNum}}</p>
                                    </div>
                                </div>
                            </div>
                            <!--Services One Single End-->
                        </div>
                    </div>
                </div>

                <div class="news-one__btn-box text-center">
                    <a href="company.html" class="thm-btn news-one__btn">查看更多公司</a>
                </div>
            </section>
            <!--Services One End-->

            <!--Services One Start-->
            <section class="services-one">
                <div class="services-one__top">
                    <div class="container">
                        <div class="section-title text-center">
                            <div class="section-sub-title-box" style="margin-left: 0;">
                                <p class="section-sub-title">为您推荐</p>
                                <div class="section-title-shape-1">
                                    <img src="static/picture/section-title-shape-1.png" alt="">
                                </div>
                                <div class="section-title-shape-2">
                                    <img src="static/picture/section-title-shape-2.png" alt="">
                                </div>
                            </div>
                            <h2 class="section-title__title">为您推荐</h2>
                        </div>
                    </div>
                </div>
                <div class="services-one__bottom">
                    <div class="services-one__container">
                        <div class="row">
                            <!--Services One Single Start-->
                            <div class="col-xl-3 col-lg-4 col-md-6 wow fadeInUp" data-wow-delay="100ms"
                                v-for="item in recommendList" :key="item" @click="handleJump(item.productsUrl,item)">
                                <div class="services-one__single">
                                    <div class="service-one__img">
                                        <img :src="item.productsImg" alt="">
                                    </div>
                                    <div class="service-one__content" style="height: 186px">
                                        <h2 class="service-one__title ellipsis-2"><a class="ellipsis-2" target="_blank"
                                                :href="item.firmUrl">{{item.name}}</a></h2>
                                        <p class="service-one__text ellipsis-2">{{item.introduction}}</p>
                                        <p class="service-one__text"><i
                                            class="far fa-eye" style="color: #015fc9"></i> {{item.clicksNum}}</p>
                                    </div>
                                </div>
                            </div>
                            <!--Services One Single End-->
                        </div>
                    </div>
                </div>
            </section>
            <!--Services One End-->

            <!--Why Choose One Start-->
            <section class="why-choose-one">
                <div class="why-choose-one-shape-1"
                    style="background-image: url(assets/images/shapes/why-choose-one-shape-1.png);"></div>
                <div class="why-choose-one-shape-2 float-bob-y">
                    <img src="static/picture/why-choose-one-shape-2.png" alt="">
                </div>
                <div class="why-choose-one-shape-3 float-bob-x">
                    <img src="static/picture/why-choose-one-shape-3.png" alt="">
                </div>
                <div class="why-choose-one-shape-4 float-bob-y">
                    <img src="static/picture/why-choose-one-shape-4.png" alt="">
                </div>
                <div class="why-choose-one-shape-5 float-bob-y">
                    <img src="static/picture/why-choose-one-shape-5.png" alt="">
                </div>
                <div class="why-choose-one-shape-6 float-bob-x">
                    <img src="static/picture/why-choose-one-shape-6.png" alt="">
                </div>
                <div class="why-choose-one-img wow slideInRight" data-wow-delay="100ms" data-wow-duration="2500ms">
                    <img src="static/picture/why-choose-one-img.png" alt="">
                </div>
                <div class="container">
                    <div class="row">
                        <div class="col-xl-6 col-lg-7">
                            <div class="why-choose-one__left">
                                <div class="section-title text-left">
                                    <div class="section-sub-title-box">
                                        <p class="section-sub-title">为什么选择{{companyInfo.companyName}}</p>
                                        <div class="section-title-shape-1">
                                            <img src="static/picture/section-title-shape-3.png" alt="">
                                        </div>
                                        <!-- <div class="section-title-shape-2">
                                            <img src="static/picture/section-title-shape-4.png" alt="">
                                        </div> -->
                                    </div>
                                    <h2 class="section-title__title">专业服务创造价值</h2>
                                </div>
                                <p class="why-choose-one__text">以客为本,想客之所想,想客之未想。真诚待客,视客如友,诚信为本,卓越服务</p>
                                <div class="why-choose-one__list-box">
                                    <ul class="list-unstyled why-choose-one__list">
                                        <li>
                                            <div class="why-choose-one__single">
                                                <div class="why-choose-one__list-icon">
                                                    <span class="icon-easy-to-use"></span>
                                                </div>
                                                <div class="why-choose-one__list-title-box">
                                                    <div class="why-choose-one__list-title-inner">
                                                        <h3 class="why-choose-one__list-title">更快的流程</h3>
                                                    </div>
                                                    <!-- <div class="why-choose-one__list-text-box">
                                                        <p class="why-choose-one__list-text">Lorem ipsum dolor sit amet,
                                                            sectetur adipiscing elit.</p>
                                                    </div> -->
                                                </div>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="why-choose-one__single">
                                                <div class="why-choose-one__list-icon">
                                                    <span class="icon-contract"></span>
                                                </div>
                                                <div class="why-choose-one__list-title-box">
                                                    <div class="why-choose-one__list-title-inner">
                                                        <h3 class="why-choose-one__list-title">更完善的保障</h3>
                                                    </div>
                                                    <!-- <div class="why-choose-one__list-text-box">
                                                        <p class="why-choose-one__list-text">Lorem ipsum dolor sit amet,
                                                            sectetur adipiscing elit.</p>
                                                    </div> -->
                                                </div>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="why-choose-one__single">
                                                <div class="why-choose-one__list-icon">
                                                    <span class="icon-policy"></span>
                                                </div>
                                                <div class="why-choose-one__list-title-box">
                                                    <div class="why-choose-one__list-title-inner">
                                                        <h3 class="why-choose-one__list-title">更好的服务</h3>
                                                    </div>
                                                    <!-- <div class="why-choose-one__list-text-box">
                                                        <p class="why-choose-one__list-text">Lorem ipsum dolor sit amet,
                                                            sectetur adipiscing elit.</p>
                                                    </div> -->
                                                </div>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="why-choose-one__single">
                                                <div class="why-choose-one__list-icon">
                                                    <span class="icon-fund"></span>
                                                </div>
                                                <div class="why-choose-one__list-title-box">
                                                    <div class="why-choose-one__list-title-inner">
                                                        <h3 class="why-choose-one__list-title">更透明的价格</h3>
                                                    </div>
                                                    <!-- <div class="why-choose-one__list-text-box">
                                                        <p class="why-choose-one__list-text">Lorem ipsum dolor sit amet,
                                                            sectetur adipiscing elit.</p>
                                                    </div> -->
                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!--Why Choose One End-->

            <!--News One Start-->
            <section class="news-one" id="news">
                <div class="container">
                    <div class="section-title text-center">
                        <div class="section-sub-title-box">
                            <p class="section-sub-title">新闻资讯</p>
                            <div class="section-title-shape-1">
                                <img src="static/picture/section-title-shape-1.png" alt="">
                            </div>
                            <div class="section-title-shape-2">
                                <img src="static/picture/section-title-shape-2.png" alt="">
                            </div>
                        </div>
                        <h2 class="section-title__title">{{companyInfo.companyName}}动态</h2>
                    </div>

                    <!-- 分类标签 -->
                    <!-- <div class="news-list-tabs">
                        <div class="news-list-tabs__nav">
                            <div class="news-list-tabs__item active" data-category="all">全部</div>
                            <div class="news-list-tabs__item" data-category="industry">行业动态</div>
                            <div class="news-list-tabs__item" data-category="policy">政策解读</div>
                            <div class="news-list-tabs__item" data-category="company">公司动态</div>
                            <div class="news-list-tabs__item" data-category="product">产品更新</div>
                        </div>
                    </div> -->

                    <!-- 全部新闻 -->
                    <div class="news-list-content active" id="all">
                        <div class="news-list">
                            <div class="news-list__item" v-for="(item,index) in newsList.slice(0, 15)" @click="toDetail(item)" :key="item.id || index">
                                <div>
                                    <!-- Removed category span -->
                                    <span class="news-list__title">{{item.name}}</span>
                                </div>
                                <div class="news-list__meta">
                                    <!-- 只有当 newsTime 不为 null 时才显示日期和日期图标 -->
                                    <span class="news-list__date" v-if="item.newsTime"><i class="far fa-calendar"></i> {{item.newsTime}}</span>
                                    <span class="news-list__views"><i class="far fa-eye"></i> {{item.clicksNum || 0}}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 行业动态 -->
                    <div class="news-list-content" id="industry">
                        <div class="news-list">
                            <div class="news-list__item" v-for="(item,index) in filteredNews('industry')" @click="toDetail(item)" :key="index">
                                <div>
                                    <span class="news-list__category">行业动态</span>
                                    <span class="news-list__title">{{item.name}}</span>
                                </div>
                                <div class="news-list__meta">
                                    <span class="news-list__date" v-if="item.newsTime"><i class="far fa-calendar"></i> {{item.newsTime}}</span>
                                    <span class="news-list__views"><i class="far fa-eye"></i> {{item.clicksNum}}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 政策解读 -->
                    <div class="news-list-content" id="policy">
                        <div class="news-list">
                            <div class="news-list__item" v-for="(item,index) in filteredNews('policy')" @click="toDetail(item)" :key="index">
                                <div>
                                    <span class="news-list__category">政策解读</span>
                                    <span class="news-list__title">{{item.name}}</span>
                                </div>
                                <div class="news-list__meta">
                                    <span class="news-list__date" v-if="item.newsTime"><i class="far fa-calendar"></i> {{item.newsTime}}</span>
                                    <span class="news-list__views"><i class="far fa-eye"></i> {{item.clicksNum}}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 公司动态 -->
                    <div class="news-list-content" id="company">
                        <div class="news-list">
                            <div class="news-list__item" v-for="(item,index) in filteredNews('company')" @click="toDetail(item)" :key="index">
                                <div>
                                    <span class="news-list__category">公司动态</span>
                                    <span class="news-list__title">{{item.name}}</span>
                                </div>
                                <div class="news-list__meta">
                                    <span class="news-list__date" v-if="item.newsTime"><i class="far fa-calendar"></i> {{item.newsTime}}</span>
                                    <span class="news-list__views"><i class="far fa-eye"></i> {{item.clicksNum}}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 产品更新 -->
                    <div class="news-list-content" id="product">
                        <div class="news-list">
                            <div class="news-list__item" v-for="(item,index) in filteredNews('product')" @click="toDetail(item)" :key="index">
                                <div>
                                    <span class="news-list__category">产品更新</span>
                                    <span class="news-list__title">{{item.name}}</span>
                                </div>
                                <div class="news-list__meta">
                                    <span class="news-list__date" v-if="item.newsTime"><i class="far fa-calendar"></i> {{item.newsTime}}</span>
                                    <span class="news-list__views"><i class="far fa-eye"></i> {{item.clicksNum}}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="news-one__btn-box text-center">
                        <a href="news.html" class="thm-btn news-one__btn">查看更多资讯</a>
                    </div>
                </div>
            </section>
            <!--News One End-->


            <!--Tracking Start-->
            <section class="tracking">
                <div class="container">
                    <div class="tracking__inner">
                        <div class="tracking-shape-1 float-bob-y">
                            <img src="static/picture/tracking-shape-1.png" alt="">
                        </div>
                        <div class="tracking-shape-2 float-bob-x">
                            <img src="static/picture/tracking-shape-2.png" alt="">
                        </div>
                        <div class="tracking-shape-3 float-bob-x">
                            <img src="static/picture/tracking-shape-3.png" alt="">
                        </div>
                        <div class="tracking-shape-4 float-bob-y">
                            <img src="static/picture/tracking-shape-4.png" alt="">
                        </div>
                        <div class="tracking__left">
                            <div class="tracking__icon">
                                <span class="icon-folder"></span>
                            </div>
                            <div class="tracking__content">
                                <p class="tracking__sub-title">为您和家人上一份保险</p>
                                <h3 class="tracking__title">立刻获取报价</h3>
                            </div>
                        </div>
                        <div class="tracking__btn-box">
                            <a class="thm-btn tracking__btn">{{companyInfo.phone}}</a>
                        </div>
                    </div>
                </div>
            </section>
            <!--Tracking End-->

            <!--Site Footer Start-->
            <footer class="site-footer">
                <div class="site-footer-bg" style="background-image: url(static/image/site-footer-bg.png);">
                </div>
                <div class="container">
                    <div class="site-footer__top">
                        <div class="row">
<!--                            <div class="col-xl-4 col-lg-8 col-md-8 wow fadeInUp" data-wow-delay="100ms">-->
<!--                                <div class="footer-widget__column footer-widget__about">-->
<!--                                    <div class="footer-widget__logo">-->
<!--                                        <a><img :src="companyInfo.logoImg" alt="" style="height: 34px"></a>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
                            <div class="col-xl-4 col-lg-8 col-md-8 wow fadeInUp" data-wow-delay="200ms">
                                <div class="footer-widget__column footer-widget__contact clearfix">
                                    <h3 class="footer-widget__title">公司地址</h3>
                                    <ul class="footer-widget__contact-list list-unstyled clearfix">
                                        <li>
                                            <div class="icon">
                                                <span class="icon-pin"></span>
                                            </div>
                                            <div class="text">
                                                <p>{{companyInfo.address}}</p>
                                            </div>
                                        </li>
                                    </ul>
                                    <div class="footer-widget__open-hour">
                                        <h3 class="footer-widget__open-hour-title">工作时间</h3>
                                        <h3 class="footer-widget__open-hour-text">工作日 早上9:00-18:00</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-4 col-lg-8 col-md-8 wow fadeInUp" data-wow-delay="400ms">
                                <div class="footer-widget__column footer-widget__newsletter">
                                    <h3 class="footer-widget__title">联系电话</h3>
                                    <div class="footer-widget__phone">
                                        <div class="footer-widget__phone-icon">
                                            <span class="icon-telephone"></span>
                                        </div>
                                        <div class="footer-widget__phone-text">
                                            <a href="tel:9200368090">{{companyInfo.phone}}</a>
                                            <p>欢迎拨打</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="site-footer__bottom">
                        <div class="row">
                            <div class="col-xl-12">
                                <div class="site-footer__bottom-inner">
                                    <p class="site-footer__bottom-text">{{companyInfo.copyright}} <a target="_blank"
                                            href="https://beian.miit.gov.cn/#/Integrated/index">{{companyInfo.icpNumber}}</a>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
            <!--Site Footer End-->


        </div><!-- /.page-wrapper -->


        <div class="mobile-nav__wrapper" :class="{expanded: expanded}">
            <div class="mobile-nav__overlay mobile-nav__toggler"></div>
            <!-- /.mobile-nav__overlay -->
            <div class="mobile-nav__content">
                <span class="mobile-nav__close mobile-nav__toggler" @click="expanded = false"><i class="fa fa-times"></i></span>

                <div class="logo-box">
                    <a aria-label="logo image"><img src="static/picture/logo-2.png" width="143"
                            alt=""></a>
                </div>
                <!-- /.logo-box -->
                <div class="mobile-nav__container"></div>
                <!-- /.mobile-nav__container -->



            </div>
            <!-- /.mobile-nav__content -->
        </div>

        <!-- 登录弹框 -->
        <div class="login-popup" :class="{ active: showLoginPopup }">
            <div class="search-popup__overlay" @click="closeLoginPopup"></div>
            <div class="search-popup__content">
                <div class="login-popup__header">
                    <h3>用户登录</h3>
                    <button type="button" class="login-popup__close" @click="closeLoginPopup">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
                <form @submit.prevent="handleLogin" class="login-popup__form">
                    <!-- 用户名输入框 -->
                    <div class="login-form-row">
                        <input type="text" v-model="loginForm.username" placeholder="请输入用户名" required>
                    </div>

                    <!-- 密码输入框 -->
                    <div class="login-form-row">
                        <input type="password" v-model="loginForm.password" placeholder="请输入密码" required>
                    </div>

                    <!-- 验证码输入框和验证码图片 -->
                    <div class="login-form-row">
                        <div class="login-sms-row">
                            <div class="login-sms-input">
                                <input type="text" v-model="loginForm.captcha" placeholder="请输入验证码" maxlength="4" required>
                            </div>
                            <div class="login-captcha-img" @click="refreshCaptcha" style="width: 130px; height: 55px; cursor: pointer; border: 1px solid #ddd; border-radius: 6px; overflow: hidden;">
                                <img :src="captchaImage" style="width: 100%; height: 100%; object-fit: cover;" alt="验证码" title="点击刷新验证码">
                            </div>
                        </div>
                    </div>

                    <!-- 登录按钮 -->
                    <div class="login-form-row">
                        <button type="submit" class="thm-btn login-submit-btn" :disabled="loginLoading">
                            {{ loginLoading ? '登录中...' : '登录' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
        <!-- /.login-popup -->

        <!-- 退出登录确认弹框 -->
        <div class="logout-confirm-popup" :class="{ active: showLogoutPopup }">
            <div class="search-popup__overlay" @click="closeLogoutPopup"></div>
            <div class="search-popup__content" style="max-width: 400px;">
                <div class="logout-confirm__content">
                    <h3>退出登录</h3>
                    <p>您好！确认要退出当前登录吗？</p>
                    <div class="logout-confirm__buttons">
                        <button type="button" @click="closeLogoutPopup" class="thm-btn logout-confirm__btn logout-confirm__btn--cancel">取消</button>
                        <button type="button" @click="handleLogout" class="thm-btn logout-confirm__btn">确认退出</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- /.logout-confirm-popup -->

    </div>
    <!-- /.mobile-nav__wrapper -->





    <a href="#" data-target="html" class="scroll-to-target scroll-to-top"><i class="fa fa-angle-up"></i></a>


    <script src="static/js/jquery-3.6.0.min.js"></script>
    <script src="static/js/bootstrap.bundle.min.js"></script>
    <script src="static/js/jarallax.min.js"></script>
    <script src="static/js/swiper.min.js"></script>
    <script src="static/js/wow.js"></script>
    <script src="static/js/jquery.appear.min.js"></script>
    <script src="static/js/jquery.validate.min.js"></script>
    <script src="static/js/timePicker.js"></script>
    <script src="static/js/jquery-ui.js"></script>
    <script src="static/js/vegas.min.js"></script>

    <!-- template js -->
    <script src="static/js/insur.js"></script>

    <script src="static/js/vue.js"></script>
    <script src="static/js/api-config.js"></script>
    <script>
        //const jeeApi = "https://www.taiyibx.com";
        //const jeeApi = "http://*************:8080";
        new Vue({
            el: '#app',
            data: {
                expanded: false,
                bannerList: [],
                newsList: [],
                businessPartner: [],
                recommendList: [],
                sortList: [],
                companyInfo: {},
                keyword: '',
                // 登录相关数据
                isLoggedIn: false,
                userInfo: null,
                showLoginPopup: false,
                showUserMenu: false,
                showLogoutPopup: false,
                loginForm: {
                    username: '',
                    password: '',
                    captcha: '',
                    checkKey: ''
                },
                captchaImage: '',
                loginLoading: false
            },
            mounted() {
                console.log('🚀 Vue应用已挂载，开始初始化...');
                this.getBanner()
                this.getNews()
                this.getBusinessPartner()
                this.getRecommend()
                this.getSortList()
                this.getCompanyInfo()
                this.checkLoginStatus()
                // 移除自动获取验证码，只在用户点击登录时才获取
                // this.refreshCaptcha()
                console.log('✅ 初始化完成');
                 // 添加标签切换事件监听
            this.$nextTick(() => {
                const categoryTabs = document.querySelectorAll('.news-list-tabs__item');
                categoryTabs.forEach(tab => {
                    tab.addEventListener('click', () => {
                        // 移除所有标签的active类
                        categoryTabs.forEach(t => t.classList.remove('active'));
                        // 给当前点击的标签添加active类
                        tab.classList.add('active');

                        // 隐藏所有内容
                        const contents = document.querySelectorAll('.news-list-content');
                        contents.forEach(content => content.classList.remove('active'));

                        // 显示对应的内容
                        const categoryId = tab.getAttribute('data-category');
                        document.getElementById(categoryId).classList.add('active');
                    });
                });
            });
        },
        beforeDestroy() {
            // 清理事件监听器
            document.removeEventListener('click', this.closeUserMenu, true);
        },
            methods: {
                // 获取轮播
                async getBanner() {
                    const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/whCarousel/list', {
                        method: 'get',
                        headers: this.getAuthHeaders()
                    })
                    const data = await response.json();
                    this.bannerList = data.result.records
                    this.$nextTick(() => {
                        window.thmSwiperInit();
                    })

                },
                filteredNews(category) {
                return this.newsList.filter(item => {
                    if (category === 'company') return item.category === '公司动态' || !item.category;
                    if (category === 'industry') return item.category === '行业动态' || item.category === '行业资讯';
                    if (category === 'policy') return item.category === '政策解读';
                    if (category === 'product') return item.category === '产品更新';
                    return true;
                });
            },

                // 获取新闻
                async getNews() {
                    try {
                        const url = `${jeeApi}/jeecg-boot/api/wechat/appNews/list/1/15`; // Fetch page 1, size 15 for index
                        const response = await fetch(url, {
                            method: 'POST', // Use POST
                            headers: {
                                'Content-Type': 'application/json' // Set headers
                            },
                            body: JSON.stringify({ name: "", type: [1] }) // Set body as specified
                        });
                        const data = await response.json();
                         if (data && data.success && data.result) {
                            this.newsList = data.result.records || [];
                         } else {
                            console.error("Failed to fetch news or invalid data format:", data);
                            this.newsList = [];
                         }
                    } catch (error) {
                        console.error("Error fetching news:", error);
                        this.newsList = [];
                    }
                },
                async toDetail(item) {
                    window.location.href = `news-details.html?id=${item.id}`
                },
                // 合作伙伴
                async getBusinessPartner() {
                    try {
                        const response = await fetch(jeeApi + '/jeecg-boot/sys/tenant/queryListPortal?pageNo=1&pageSize=20', {
                            method: 'get',
                            headers: this.getAuthHeaders()
                        });
                        const data = await response.json();

                        if (data && data.success && data.result) {
                            this.businessPartner = data.result.records || [];
                        } else {
                            console.error("获取公司列表失败或数据格式无效:", data);
                            this.businessPartner = [];
                        }
                    } catch (error) {
                        console.error("获取公司列表出错:", error);
                        this.businessPartner = [];
                    }
                },
                // 门户标签
                async getSortList() {
                    const response = await fetch(jeeApi + '/jeecg-boot/api/excel/whIntroduction/queryOne', {
                        method: 'get',
                        headers: this.getAuthHeaders()
                    })
                    const data = await response.json();
                    this.sortList = data.result

                },
                // 推荐
                async getRecommend() {
                    const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/whProducts/list', {
                        method: 'get',
                        headers: this.getAuthHeaders()
                    })
                    const data = await response.json();
                    this.recommendList = data.result.records

                },
                // 推荐
                async getCompanyInfo() {
                    const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/whHome/getOne', {
                        method: 'get',
                        headers: this.getAuthHeaders()
                    })
                    const data = await response.json();
                    this.companyInfo = data.result
                    document.title =  this.companyInfo.name

                },
                // 跳转
                handleJump(url,item) {
                    window.open(url)
                },

                // 处理公司点击事件
                async handleCompanyClick(item) {
                    // 先在本地将点击数加一，提升用户体验
                    if (item.clickNum !== undefined) {
                        item.clickNum = (parseInt(item.clickNum) || 0) + 1;
                    }

                    // 记录点击次数到按小时统计报表，使用租户ID
                    if (item.id) {
                        try {
                            // 调用新的点击报表接口
                            await fetch(jeeApi + '/jeecg-boot/click-report-hourly/addClickByTenantId?tenantId=' + item.id, {
                                method: 'POST',
                                headers: this.getAuthHeaders()
                            });
                        } catch (error) {
                            console.error('记录点击数失败:', error);
                        }

                        // 保留原有的点击记录接口（如果需要）
                        try {
                            await fetch(jeeApi + '/jeecg-boot/firmInformation/whClick/addOne?clicksId=' + item.id, {
                                method: 'get',
                                headers: this.getAuthHeaders()
                            });
                        } catch (error) {
                            console.error('记录原有点击数失败:', error);
                        }
                    }

                    // 如果有网址，则跳转到网址
                    if (item.firmUrl) {
                        window.open(item.firmUrl);
                    } else {
                        // 如果没有网址，则跳转到公司详情页面，传递租户ID
                        window.open('company-details.html?id=' + encodeURIComponent(item.id));
                    }
                },

                // 通用方法：获取请求头（包含token）
                getAuthHeaders() {
                    const headers = {
                        'Content-Type': 'application/json'
                    };

                    const token = localStorage.getItem('X-Access-Token');
                    if (token) {
                        headers['X-Access-Token'] = token;
                    }

                    return headers;
                },

                // 登录相关方法
                checkLoginStatus() {
                    console.log('🔍 检查登录状态...');
                    const token = localStorage.getItem('X-Access-Token');
                    const userInfo = localStorage.getItem('userInfo');
                    console.log('📝 Token:', token ? '存在' : '不存在');
                    console.log('👤 UserInfo:', userInfo ? '存在' : '不存在');

                    if (token && userInfo) {
                        this.isLoggedIn = true;
                        this.userInfo = JSON.parse(userInfo);
                        console.log('✅ 用户已登录:', this.userInfo);
                    } else {
                        console.log('❌ 用户未登录');
                    }
                },

                handleUserClick(event) {
                    event.stopPropagation();

                    if (this.isLoggedIn) {
                        this.showUserMenu = !this.showUserMenu;

                        // 如果菜单打开，添加全局点击监听器
                        if (this.showUserMenu) {
                            this.$nextTick(() => {
                                document.addEventListener('click', this.closeUserMenu, true);
                            });
                        } else {
                            document.removeEventListener('click', this.closeUserMenu, true);
                        }
                    } else {
                        // 在显示登录弹框时才获取验证码
                        this.refreshCaptcha();
                        this.showLoginPopup = true;
                    }
                },

                closeUserMenu(event) {
                    // 检查点击是否在用户菜单区域内
                    const userContainer = event.target.closest('.main-menu__user-container');

                    if (!userContainer) {
                        this.showUserMenu = false;
                        document.removeEventListener('click', this.closeUserMenu, true);
                    }
                },

                closeLoginPopup() {
                    this.showLoginPopup = false;
                    this.resetLoginForm();
                },

                resetLoginForm() {
                    this.loginForm = {
                        username: '',
                        password: '',
                        captcha: '',
                        checkKey: ''
                    };
                    this.loginLoading = false;
                    this.refreshCaptcha();
                },

                async refreshCaptcha() {
                    try {
                        // 生成随机的checkKey
                        this.loginForm.checkKey = Math.random().toString(36).substring(2, 15);

                        console.log('🔄 获取验证码，checkKey:', this.loginForm.checkKey);

                        const response = await fetch(`${jeeApi}/jeecg-boot/sys/randomImage/${this.loginForm.checkKey}`, {
                            method: 'GET'
                        });
                        const data = await response.json();

                        console.log('🔄 验证码接口响应:', data);

                        if (data.success) {
                            // 后端返回的result已经包含了data:image/jpg;base64,前缀
                            this.captchaImage = data.result;
                            console.log('✅ 验证码图片设置成功');
                        } else {
                            console.error('❌ 获取验证码失败:', data.message);
                            alert('获取验证码失败: ' + (data.message || '未知错误'));
                        }
                    } catch (error) {
                        console.error('❌ 获取验证码请求失败:', error);
                        alert('获取验证码失败，请稍后重试');
                    }
                },

                async handleLogin() {
                    if (!this.loginForm.username || !this.loginForm.password || !this.loginForm.captcha) {
                        alert('请输入用户名、密码和验证码');
                        return;
                    }

                    this.loginLoading = true;

                    try {
                        const response = await fetch(`${jeeApi}/jeecg-boot/sys/login`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                username: this.loginForm.username,
                                password: this.loginForm.password,
                                captcha: this.loginForm.captcha,
                                checkKey: this.loginForm.checkKey
                            })
                        });

                        const data = await response.json();

                        if (data.success) {
                            // 保存登录信息
                            localStorage.setItem('X-Access-Token', data.result.token);
                            localStorage.setItem('userInfo', JSON.stringify(data.result.userInfo));

                            this.isLoggedIn = true;
                            this.userInfo = data.result.userInfo;
                            this.closeLoginPopup();

                            alert('登录成功');
                        } else {
                            alert(data.message || '登录失败');
                        }
                    } catch (error) {
                        console.error('登录失败:', error);
                        alert('登录失败，请稍后重试');
                    } finally {
                        this.loginLoading = false;
                    }
                },

                showLogoutConfirm() {
                    this.showUserMenu = false;
                    this.showLogoutPopup = true;
                    // 移除事件监听器
                    document.removeEventListener('click', this.closeUserMenu, true);
                },

                closeLogoutPopup() {
                    this.showLogoutPopup = false;
                },

                async handleLogout() {
                    try {
                        const token = localStorage.getItem('X-Access-Token');
                        if (token) {
                            await fetch(`${jeeApi}/jeecg-boot/sys/logout`, {
                                method: 'POST',
                                headers: {
                                    'X-Access-Token': token
                                }
                            });
                        }
                    } catch (error) {
                        console.error('退出登录请求失败:', error);
                    } finally {
                        // 清除本地存储
                        localStorage.removeItem('X-Access-Token');
                        localStorage.removeItem('userInfo');

                        this.isLoggedIn = false;
                        this.userInfo = null;
                        this.closeLogoutPopup();

                        alert('已退出登录');
                    }
                }
            }
        })
    </script>
</body>

</html>
